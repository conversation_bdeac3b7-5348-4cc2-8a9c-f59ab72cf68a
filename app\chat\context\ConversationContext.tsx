import {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
  ReactNode,
} from "react";
import { debounce } from "lodash-es";
import { useTranslation } from "react-i18next";
import { Conversation } from "../components/sidebar";
import {
  fetchConversationList,
  fetchMessagesByConversionId,
  updateConversation,
  deleteConversation,
  feedbackMessage,
  fetchStopChatMessage,
} from "@/services/api";
import { get, set } from "idb-keyval";
import { streamSSE } from "@/services/hanlderSSE";
import { v4 as uuidv4 } from "uuid";

// 聊天类型定义
export type ChatType = "data" | "knowledge" | "web" | "deep";

// 聊天选项定义
export interface ChatOption {
  id: ChatType;
  label: string;
  icon: string;
  description: string;
}

// 通过 i18n 生成默认聊天选项
export const buildChatOptions = (t: (key: string) => string): ChatOption[] => [
  {
    id: "knowledge",
    label: t("chat.options.knowledge.label"),
    icon: "📚",
    description: t("chat.options.knowledge.desc"),
  },
  {
    id: "web",
    label: t("chat.options.web.label"),
    icon: "🔍",
    description: t("chat.options.web.desc"),
  },
  {
    id: "deep",
    label: t("chat.options.deep.label"),
    icon: "🧠",
    description: t("chat.options.deep.desc"),
  },
];

// 消息类型定义
export interface Message {
  id: string;
  content: string;
  role: "user" | "assistant";
  created_at: string;
  chart_data?: any; // ECharts 配置对象
  retrieval_sql?: string; // SQL 查询语句
  retrieval_result?: Array<Record<string, any>>; // 表格数据
  metadata?: Record<string, any>; // 其他元数据
  thought_data?: any; // 思考过程数据
  feedback?: 1 | 2 | null; // 1 点赞、2 点踩、null 无反馈
}

// 上下文类型定义
interface ConversationContextType {
  conversations: Conversation[];
  activeConversationId: string | null;
  messages: Message[];
  loading: boolean;
  error: string | null;
  isStreaming: boolean;
  streamingConversationId: string | null;
  chatType: ChatType;
  chatOptions: ChatOption[];
  setActiveConversationId: (id: string | null) => void;
  setChatType: (type: ChatType) => void;
  loadConversations: (preserveLocal?: boolean) => Promise<void>;
  loadMessages: (conversationId: string) => Promise<void>;
  createNewConversation: (tempId?: string) => Promise<string | undefined>;
  sendMessage: (
    content: string,
    conversationId?: string
  ) => Promise<(() => void) | undefined>;
  regenerateMessage: (assistantMessageId: string) => Promise<(() => void) | undefined>;
  sendFeedback: (messageId: string, feedback: 1 | 2) => Promise<void>;
  stopGeneration: () => void;
  clearMessages: () => void;
  updateConversationTitle: (id: string, title: string) => Promise<void>;
  deleteConversationById: (id: string) => Promise<void>;
}

// 创建上下文
const ConversationContext = createContext<ConversationContextType | undefined>(
  undefined
);

// 自定义 hook
export const useConversation = () => {
  const context = useContext(ConversationContext);
  if (!context) {
    throw new Error(
      "useConversation must be used within a ConversationProvider"
    );
  }
  return context;
};

// Provider 属性类型
interface ConversationProviderProps {
  children: ReactNode;
}

// 主 Provider 组件
export const ConversationProvider: React.FC<ConversationProviderProps> = ({
  children,
}) => {
  const { t } = useTranslation();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, _setActiveConversationId] = useState<
    string | null
  >(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [streamingConversationId, setStreamingConversationId] = useState<
    string | null
  >(null);
  const [error, setError] = useState<string | null>(null);
  const [chatType, setChatType] = useState<ChatType>("data"); // 默认为数据表类型
  const chatOptions = useMemo<ChatOption[]>(() => buildChatOptions(t), [t]); // 可用的聊天选项（国际化）
  const abortControllerRef = useRef<{ abort: () => void } | null>(null);
  const pageSize = 100; // 默认请求 100 条
  const hasLocalConversationsRef = useRef<boolean>(false); // 跟踪是否有本地创建的会话
  // 流式更新节流支持（使用 lodash-es debounce）
  const streamPendingDataRef = useRef<any | null>(null);
  const streamAssistantIdRef = useRef<string | null>(null);
  // 记录服务端返回的流式任务 ID，用于停止接口
  const streamTaskIdRef = useRef<string | null>(null);

  const flushStreamUpdate = useCallback(() => {
    const data = streamPendingDataRef.current;
    const assistantMessageId = streamAssistantIdRef.current;
    if (!data || !assistantMessageId) return;
    // 仅当数据实际发生变化时才更新，避免无意义的重复渲染
    setMessages((prevMessages) => {
      let changed = false;
      const next = prevMessages.map((msg) => {
        if (msg.id !== assistantMessageId || msg.role !== "assistant") return msg;
        const nextMsg = {
          ...msg,
          content: data.answer !== undefined ? data.answer : msg.content,
          chart_data:
            data.chart_data !== undefined ? data.chart_data : msg.chart_data,
          retrieval_sql:
            data.retrieval_sql !== undefined
              ? data.retrieval_sql
              : msg.retrieval_sql,
          retrieval_result:
            data.retrieval_result !== undefined
              ? data.retrieval_result
              : msg.retrieval_result,
          metadata: data.metadata !== undefined ? data.metadata : msg.metadata,
          thought_data:
            data.thought_data !== undefined
              ? data.thought_data
              : msg.thought_data,
        } as typeof msg;

        const contentChanged = nextMsg.content !== msg.content;
        const chartChanged =
          JSON.stringify(nextMsg.chart_data) !== JSON.stringify(msg.chart_data);
        const sqlChanged =
          JSON.stringify(nextMsg.retrieval_sql) !==
          JSON.stringify(msg.retrieval_sql);
        const resultChanged =
          JSON.stringify(nextMsg.retrieval_result) !==
          JSON.stringify(msg.retrieval_result);
        const metadataChanged =
          JSON.stringify(nextMsg.metadata) !== JSON.stringify(msg.metadata);
        const thoughtChanged =
          JSON.stringify(nextMsg.thought_data) !==
          JSON.stringify(msg.thought_data);

        if (
          contentChanged ||
          chartChanged ||
          sqlChanged ||
          resultChanged ||
          metadataChanged ||
          thoughtChanged
        ) {
          changed = true;
          return nextMsg;
        }
        return msg;
      });
      return changed ? next : prevMessages;
    });
    // 清空待处理数据
    streamPendingDataRef.current = null;
  }, []);

  // 创建去抖的 flush（仅尾随执行，确保最后一块一定落地）
  const debouncedFlushRef = useRef(
    debounce(
      () => {
        flushStreamUpdate();
      },
      60,
      { leading: false, trailing: true }
    )
  );

  // 自定义的 setActiveConversationId 函数，处理会话切换
  const setActiveConversationId = useCallback((id: string | null) => {
    _setActiveConversationId(id);
  }, []);

  // 辅助函数：更新缓存
  const updateCache = async (
    conversationId: string,
    messages: Message[],
    conversations: Conversation[]
  ) => {
    try {
      const cachedData = await get<{
        conversations: Conversation[];
        messages: { [conversationId: string]: Message[] };
      }>("conversations_data");

      const newCachedData = {
        conversations,
        messages: {
          ...(cachedData?.messages || {}),
          [conversationId]: messages,
        },
      };
      await set("conversations_data", newCachedData);
    } catch (error) {
      console.error("更新缓存失败:", error);
    }
  };

  // 发送点赞/点踩反馈并更新本地消息
  const sendFeedback = async (messageId: string, fb: 1 | 2) => {
    if (!messageId || messageId.startsWith("temp_")) return;
    // 乐观更新
    setMessages((prev) =>
      prev.map((m) => (m.id === messageId ? { ...m, feedback: fb } : m))
    );
    try {
      await feedbackMessage(messageId, fb);
      // 成功后落缓存
      if (activeConversationId) {
        const cachedData = await get<{
          conversations: Conversation[];
          messages: { [conversationId: string]: Message[] };
        }>("conversations_data");
        const convs = cachedData?.conversations || conversations;
        const msgs = (
          cachedData?.messages?.[activeConversationId] || messages
        ).map((m) => (m.id === messageId ? { ...m, feedback: fb } : m));
        await updateCache(activeConversationId, msgs, convs);
      }
    } catch (e) {
      console.error("发送反馈失败:", e);
      // 回滚
      setMessages((prev) =>
        prev.map((m) => (m.id === messageId ? { ...m, feedback: null } : m))
      );
    }
  };

  // 仅重新生成某条 AI 消息（不新增用户消息）
  const regenerateMessage = async (
    assistantMessageId: string
  ) => {
    const targetConversationId = activeConversationId;
    if (!targetConversationId) return;
    if (isStreaming) return;

    // 定位到需要重试的 AI 消息，确保只匹配 assistant 角色的消息
    const idx = messages.findIndex((m) => m.id === assistantMessageId && m.role === "assistant");
    if (idx === -1) return;

    // 向前找到紧邻的上一条用户消息作为查询来源
    // 通常用户消息和助手消息是成对出现的，所以找到助手消息的前一条应该就是对应的用户消息
    let prevUserMsg: Message | undefined;
    for (let i = idx - 1; i >= 0; i--) {
      if (messages[i].role === "user") {
        prevUserMsg = messages[i];
        break;
      }
    }
    if (!prevUserMsg) return;

    try {
      setIsStreaming(true);
      setStreamingConversationId(targetConversationId);

      // 记录当前本次流式 assistant 消息 id，供节流 flush 使用
      streamAssistantIdRef.current = assistantMessageId;

      // 清空该 AI 消息的内容与可视化数据，为新的流式内容让路
      setMessages((prev) =>
        prev.map((m) =>
          m.id === assistantMessageId && m.role === "assistant"
            ? {
                ...m,
                content: "",
                chart_data: undefined,
                retrieval_sql: undefined,
                retrieval_result: undefined,
                metadata: undefined,
                thought_data: undefined,
              }
            : m
        )
      );

      const chatUrl = getChatUrl(chatType);
      const abortFunction = await streamSSE(
        `${process.env.NEXT_PUBLIC_API_URL}${chatUrl}`,
        {
          query: prevUserMsg.content,
          conversation_id: targetConversationId.startsWith("temp_")
            ? ""
            : targetConversationId,
          message_id: assistantMessageId,
        },
        {
          onMessage: (data: any) => {
            // 复用统一的流式去抖逻辑
            streamPendingDataRef.current = data;
            debouncedFlushRef.current();
            // 捕获任务ID
            if (data && typeof data.task_id === "string" && data.task_id) {
              streamTaskIdRef.current = data.task_id;
            }
          },
          onError: (error: Error) => {
            console.error("Regenerate stream error:", error);
            setError(t("chat.errors.sendMessage"));
            setIsStreaming(false);
            setStreamingConversationId(null);
            abortControllerRef.current = null;
            // 尝试把最后一块落地
            debouncedFlushRef.current.flush();
            setMessages((prevMessages) =>
              prevMessages.map((msg) =>
                msg.id === assistantMessageId && msg.role === "assistant"
                  ? { ...msg, content: t("chat.errors.streamError") }
                  : msg
              )
            );
            // 清理任务ID
            streamTaskIdRef.current = null;
          },
          onFinish: (finalData: any) => {
            setIsStreaming(false);
            setStreamingConversationId(null);
            abortControllerRef.current = null;
            debouncedFlushRef.current.flush();

            // 与 sendMessage 不同：保持原有 message.id，不做替换
            let finalMessagesSnapshot: Message[] = [];
            setMessages((prev) => {
              const next = prev.map((msg) => {
                if (msg.id !== assistantMessageId || msg.role !== "assistant") return msg;
                return {
                  ...msg,
                  content:
                    finalData.answer || msg.content || t("chat.errors.noResponse"),
                  chart_data: finalData.chart_data || msg.chart_data,
                  retrieval_sql: finalData.retrieval_sql || msg.retrieval_sql,
                  retrieval_result: finalData.retrieval_result || msg.retrieval_result,
                  metadata: finalData.metadata || msg.metadata,
                  thought_data: finalData.thought_data || msg.thought_data,
                };
              });
              finalMessagesSnapshot = next;
              return next;
            });

            // 更新缓存
            if (targetConversationId) {
              updateCache(targetConversationId, finalMessagesSnapshot, conversations);
            }

            // 清理本次流式上下文
            streamAssistantIdRef.current = null;
            streamPendingDataRef.current = null;
            streamTaskIdRef.current = null;
          },
        }
      );

      abortControllerRef.current = { abort: abortFunction };
      return abortFunction;
    } catch (err) {
      console.error("重新生成消息错误:", err);
      setError(t("chat.errors.sendMessage"));
      setIsStreaming(false);
      setStreamingConversationId(null);
    }
  };

  // 加载会话列表（一次性加载 100 条）
  const loadConversations = useCallback(
    async (preserveLocal = false) => {
      setLoading(true);
      setError(null);

      try {
        // 请求 100 条数据
        const { data } = (await fetchConversationList({
          page: 1,
          page_num: pageSize,
        })) as any;

        // 始终保留本地会话，避免被服务器数据覆盖
        setConversations((prev) => {
          const serverConversations = data || [];
          const localConversations = prev.filter(
            (conv) =>
              conv.id.startsWith("temp_") ||
              !serverConversations.find(
                (serverConv: any) => serverConv.id === conv.id
              )
          );

          // 合并：本地会话在前，服务器会话在后
          return [...localConversations, ...serverConversations];
        });

        // 不自动激活第一个会话，让页面组件自己决定
        // 这样可以避免在新会话页面时自动加载其他会话的消息

        await updateCache("", [], data || []);
      } catch (err) {
        setError(t("chat.errors.loadConversations"));
        console.error("加载会话列表错误:", err);
        // 即使出错也要保留本地会话
      } finally {
        setLoading(false);
      }
    },
    [t, pageSize]
  );

  // 加载指定会话的消息
  const loadMessages = useCallback(
    async (conversationId: string) => {
      setLoading(true);
      setError(null);

      try {
        // 如果是临时会话ID，清空消息列表，准备新会话
        if (conversationId.startsWith("temp_")) {
          setMessages([]);
          setLoading(false);
          return;
        }

        // 从统一缓存结构中获取消息
        const cachedData = await get<{
          conversations: Conversation[];
          messages: { [conversationId: string]: Message[] };
        }>("conversations_data");

        const cachedMessages = cachedData?.messages?.[conversationId];

        if (
          cachedMessages &&
          Array.isArray(cachedMessages) &&
          cachedMessages.length > 0
        ) {
          setMessages(cachedMessages);
        } else {
          const { data } = (await fetchMessagesByConversionId(
            conversationId
          )) as any;
          const messages = data.messages || [];
          setMessages(messages);

          // 更新统一缓存结构
          await updateCache(
            conversationId,
            messages,
            cachedData?.conversations || []
          );
        }
      } catch (err) {
        setError(t("chat.errors.loadMessages"));
        console.error("加载消息错误:", err);
        setMessages([]);
      } finally {
        setLoading(false);
      }
    },
    [t]
  );

  // 根据聊天类型获取API URL
  const getChatUrl = useCallback((type: ChatType) => {
    switch (type) {
      case "knowledge":
        return `/api/chat/chat2knowledge`;
      case "web":
        return `/api/chat/chat2web`;
      case "deep":
        return `/api/chat/deepReserach`;
      case "data":
      default:
        return `/api/chat/chat2db`;
    }
  }, []);

  // 创建新会话（本地）
  const createNewConversation = useCallback(
    async (tempId?: string): Promise<string | undefined> => {
      try {
        const newConversation: Conversation = {
          id: tempId || Date.now().toString(),
          title: t("chat.newConversation"),
          created_at: new Date().toLocaleString(),
        };

        setConversations((prev) => [newConversation, ...prev]);
        _setActiveConversationId(newConversation.id);

        // 标记有本地创建的会话
        hasLocalConversationsRef.current = true;

        // 创建新会话时清空消息，确保是干净的状态
        setMessages([]);
        return newConversation.id;
      } catch (err) {
        setError(t("chat.errors.createConversation"));
        console.error("创建会话错误:", err);
        return undefined;
      }
    },
    [t]
  );

  // 停止生成
  const stopGeneration = useCallback(() => {
    // 1) 先 fire-and-forget 调用后端停止接口（如果拿到了任务ID）
    const taskId = streamTaskIdRef.current;
    if (taskId) {
      try {
        void fetchStopChatMessage(taskId).catch(() => {});
      } catch (_) {
        // 忽略任何错误，避免阻塞 UI 停止
      }
    }

    // 2) 立即中止本地流与状态，确保按钮“立刻生效”
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsStreaming(false);
    setStreamingConversationId(null);
    // 3) 清理任务ID，防止后续误用
    streamTaskIdRef.current = null;
  }, []);

  // 清空消息
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // 更新会话标题
  const updateConversationTitle = useCallback(
    async (id: string, title: string) => {
      try {
        setLoading(true);

        // 如果不是临时会话，调用 API 更新
        if (!id.startsWith("temp_")) {
          await updateConversation(id, title);
        }

        // 更新本地状态
        setConversations((prev) =>
          prev.map((conv) => (conv.id === id ? { ...conv, title } : conv))
        );

        // 更新缓存
        const cachedData = await get<{
          conversations: Conversation[];
          messages: { [conversationId: string]: Message[] };
        }>("conversations_data");

        if (cachedData) {
          const updatedConversations = cachedData.conversations.map((conv) =>
            conv.id === id ? { ...conv, title } : conv
          );
          await set("conversations_data", {
            ...cachedData,
            conversations: updatedConversations,
          });
        }
      } catch (err) {
        setError(t("chat.errors.updateConversation"));
        console.error("更新会话标题错误:", err);
      } finally {
        setLoading(false);
      }
    },
    [t]
  );

  // 删除会话
  const deleteConversationById = useCallback(
    async (id: string) => {
      try {
        setLoading(true);

        // 如果不是临时会话，调用 API 删除
        if (!id.startsWith("temp_")) {
          await deleteConversation(id);
        }

        // 更新本地状态
        setConversations((prev) => prev.filter((conv) => conv.id !== id));

        // 如果删除的是当前激活的会话，清空相关状态
        if (activeConversationId === id) {
          _setActiveConversationId(null);
          setMessages([]);
        }

        // 更新缓存
        const cachedData = await get<{
          conversations: Conversation[];
          messages: { [conversationId: string]: Message[] };
        }>("conversations_data");

        if (cachedData) {
          const updatedConversations = cachedData.conversations.filter(
            (conv) => conv.id !== id
          );
          const updatedMessages = { ...cachedData.messages };
          delete updatedMessages[id];

          await set("conversations_data", {
            conversations: updatedConversations,
            messages: updatedMessages,
          });
        }
      } catch (err) {
        setError(t("chat.errors.deleteConversation"));
        console.error("删除会话错误:", err);
      } finally {
        setLoading(false);
      }
    },
    [activeConversationId, t]
  );

  // 发送消息（使用流式API）
  const sendMessage = async (
    content: string,
    conversationId?: string,
  ) => {
    const targetConversationId = conversationId || activeConversationId;
    if (!content.trim() || !targetConversationId) return;
    if (isStreaming) return;

    // chatType 选择：优先使用 UI 选择项，其次回落到全局 chatType
    const currentChatType = chatType;

    const userMessage: Message = {
      id: `temp_${uuidv4()}`,
      content,
      role: "user",
      created_at: new Date().toISOString(),
    };

    const assistantMessageId = `temp_${uuidv4()}`;
    // 记录当前本次流式 assistant 消息 id，供节流 flush 使用
    streamAssistantIdRef.current = assistantMessageId;
    const assistantMessage: Message = {
      id: assistantMessageId,
      content: "",
      role: "assistant",
      created_at: new Date().toISOString(),
    };

    const updatedMessages = [...messages, userMessage, assistantMessage];
    setMessages(updatedMessages);
    setConversations((prevConversations) => {
      const updatedConversations = prevConversations.map((conv) =>
        conv.id === targetConversationId
          ? { ...conv, created_at: new Date().toISOString() }
          : conv
      );

      if (targetConversationId) {
        updateCache(
          targetConversationId,
          updatedMessages,
          updatedConversations
        );
      }

      return updatedConversations;
    });

    try {
      setIsStreaming(true);
      setStreamingConversationId(targetConversationId || null);

      const chatUrl = getChatUrl(currentChatType);
      const abortFunction = await streamSSE(
        `${process.env.NEXT_PUBLIC_API_URL}${chatUrl}`,
        {
          query: content,
          conversation_id: targetConversationId?.startsWith("temp_")
            ? ""
            : targetConversationId,
        },
        {
          onMessage: (data: any) => {
            // 轻量节流（去抖）+ 尾随：累计最新分片，合并更新
            streamPendingDataRef.current = data;
            debouncedFlushRef.current();
            // 捕获任务ID
            if (data && typeof data.task_id === "string" && data.task_id) {
              streamTaskIdRef.current = data.task_id;
            }
          },
          onError: (error: Error) => {
            console.error("Stream error:", error);
            setError(t("chat.errors.sendMessage"));
            setIsStreaming(false);
            setStreamingConversationId(null);
            abortControllerRef.current = null;
            // 发生错误前尝试把已累计的最后一块内容落地
            debouncedFlushRef.current.flush();
            setMessages((prevMessages) =>
              prevMessages.map((msg) =>
                msg.id === assistantMessageId && msg.role === "assistant"
                  ? { ...msg, content: t("chat.errors.streamError") }
                  : msg
              )
            );
            // 清理任务ID
            streamTaskIdRef.current = null;
          },
          onFinish: (finalData: any) => {
            setIsStreaming(false);
            setStreamingConversationId(null);
            abortControllerRef.current = null;
            // 在最终合并前，强制 flush 一次，确保最后分片落地
            debouncedFlushRef.current.flush();

            const serverMessageId =
              finalData.message_id || assistantMessageId.replace("temp_", "");
            const serverUserMessageId = finalData.serverMessageId;
            const realConversationId = finalData.conversation_id;
            let finalConversationId = targetConversationId;

            // 使用函数式更新，避免使用过期的 updatedMessages 闭包
            let finalMessagesSnapshot: Message[] = [];
            setMessages((prev) => {
              const next = prev.map((msg) => {
                // 更新助手消息
                if (msg.id === assistantMessageId && msg.role === "assistant") {
                  return {
                    ...msg,
                    id: serverMessageId,
                    content:
                      finalData.answer ||
                      msg.content ||
                      t("chat.errors.noResponse"),
                    chart_data: finalData.chart_data || msg.chart_data,
                    retrieval_sql: finalData.retrieval_sql || msg.retrieval_sql,
                    retrieval_result:
                      finalData.retrieval_result || msg.retrieval_result,
                    metadata: finalData.metadata || msg.metadata,
                    thought_data: finalData.thought_data || msg.thought_data,
                  };
                }
                // 更新用户消息的 ID（如果服务器返回了用户消息 ID）
                if (msg.role === "user" && msg.id.startsWith("temp_") && serverUserMessageId) {
                  return {
                    ...msg,
                    id: serverUserMessageId,
                  };
                }
                return msg;
              });
              finalMessagesSnapshot = next;
              return next;
            });

            if (
              targetConversationId?.startsWith("temp_") &&
              realConversationId
            ) {
              finalConversationId = realConversationId;
              _setActiveConversationId(realConversationId);
              if (typeof window !== "undefined") {
                window.history.replaceState(
                  {},
                  "",
                  `/chat/${realConversationId}`
                );
              }

              setConversations((prevConversations) => {
                const newConversations = prevConversations.map((conv) =>
                  conv.id === targetConversationId
                    ? {
                        ...conv,
                        id: realConversationId,
                        title: finalData.title || conv.title,
                        created_at: new Date().toISOString(),
                      }
                    : conv
                );
                updateCache(
                  realConversationId,
                  finalMessagesSnapshot,
                  newConversations
                );
                return newConversations;
              });

              hasLocalConversationsRef.current = false;
            } else if (finalConversationId) {
              updateCache(
                finalConversationId,
                finalMessagesSnapshot,
                conversations
              );
            }
            // 清理本次流式上下文
            streamAssistantIdRef.current = null;
            streamPendingDataRef.current = null;
          },
        }
      );

      abortControllerRef.current = { abort: abortFunction };
      return abortFunction;
    } catch (err) {
      console.error("发送消息错误:", err);
      setError(t("chat.errors.sendMessage"));
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.id === assistantMessageId && msg.role === "assistant"
            ? { ...msg, content: t("chat.errors.sendMessage") }
            : msg
        )
      );
    }
  };

  // 监听 activeConversationId 变化，加载对应消息
  useEffect(() => {
    if (activeConversationId) {
      loadMessages(activeConversationId);
    } else {
      setMessages([]);
    }
  }, [activeConversationId, loadMessages]);

  // 初始化：加载会话列表
  useEffect(() => {
    loadConversations(true); // 初始化时保留本地会话
  }, []); // 只在组件挂载时执行一次

  // 提供给 Context 的值
  const contextValue: ConversationContextType = {
    conversations,
    activeConversationId,
    messages,
    loading,
    error,
    isStreaming,
    streamingConversationId,
    chatType,
    chatOptions,
    setActiveConversationId,
    setChatType,
    loadConversations,
    loadMessages,
    createNewConversation,
    sendMessage,
    regenerateMessage,
    sendFeedback,
    stopGeneration,
    clearMessages,
    updateConversationTitle,
    deleteConversationById,
  };

  return (
    <ConversationContext.Provider value={contextValue}>
      {children}
    </ConversationContext.Provider>
  );
};
