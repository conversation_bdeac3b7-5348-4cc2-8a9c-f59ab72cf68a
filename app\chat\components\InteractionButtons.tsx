import { Button } from "@/components/ui/button";
import { RefreshCw, Copy, ThumbsUp, ThumbsDown, Check } from "lucide-react";

interface InteractionButtonsProps {
  onRetry?: () => void;
  onCopy?: () => void;
  onLike?: () => void;
  onDislike?: () => void;
  feedback?: 1 | 2 | null;
  copied?: boolean;
  // 是否禁用重试按钮（例如流式生成期间）
  disabledRetry?: boolean;
  // 是否显示重试按钮
  showRetry?: boolean;
}

export const InteractionButtons: React.FC<InteractionButtonsProps> = ({
  onRetry,
  onCopy,
  onLike,
  onDislike,
  feedback,
  copied,
  disabledRetry,
  showRetry = true,
}) => {
  return (
    <div className="flex gap-1">
      {showRetry && (
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full"
          aria-label="重新发起"
          onClick={onRetry}
          disabled={!!disabledRetry}
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      )}
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="复制"
        onClick={onCopy}
      >
        {copied ? (
          <Check className="h-4 w-4 text-green-600" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="点赞"
        onClick={onLike}
      >
        <ThumbsUp
          className={`h-4 w-4 ${feedback === 1 ? "text-primary" : ""}`}
        />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full"
        aria-label="点踩"
        onClick={onDislike}
      >
        <ThumbsDown
          className={`h-4 w-4 ${feedback === 2 ? "text-red-500" : ""}`}
        />
      </Button>
    </div>
  );
};
